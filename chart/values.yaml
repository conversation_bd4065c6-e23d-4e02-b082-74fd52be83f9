



initContainerConfig: {}



automountServiceAccountToken: true

ingress:
  enabled: false
  annotations: {}
  hosts:
    - host: chart-example.local
      paths: []

  tls: []

alb_ingress:
  enabled: false

virtualService:
  enabled: false

internalIngress: {}
nodeSelector: {}

tolerations: []

affinity: {}

cronjob:
  enabled: false

job:
  enabled: false





nameOverride: platform-frontend
fullnameOverride: platform-frontend
app: o2
replicaCount: 1

image:
  registry: "************.dkr.ecr.us-east-1.amazonaws.com"
  name: "veracode/sca-platform-frontend"
  tag: "ATL-3807-c7bcce81"

imageConfig:
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80
  annotations:
    ingress.kubernetes.io/named-ports: '{"http":"80"}'

annotations:
  prometheus.io/port: '9145'
  prometheus.io/scrape: 'true'
ports: |
  - containerPort: 80
    name: http
    protocol: TCP
  - containerPort: 9145
    name: prometheus
    protocol: TCP
env:
  CLOUDFRONT_HOST: https://app-__ENV_BRANCH__.__ENV_NAME__.srcclr.io
  DOWNLOAD_HOST: https://download-__ENV_BRANCH__.__ENV_NAME__.srcclr.io
valueFrom:
  - name: ENV_JS
    value: |
      window.SRCCLR_ENV = {
        API_URL: 'https://api-__ENV_BRANCH__.__ENV_NAME__.srcclr.io',
        VC_API_URL: 'https://ui-agora-__ENV_BRANCH__.__ENV_NAME__.veracode.io/srcclr',
        BEARER_DOMAINS: ['__ENV_NAME__.srcclr.io', 'localhost'],
        VERACODE_DOMAINS: ['sca-__ENV_BRANCH__.__ENV_NAME__.veracode.io'],
        BILLING_URL: 'https://pay.ksp.o2.srcclr.io',
        ENVIRONMENT: 'QA',
        FRONTEND_HOST_URL: 'https://app-__ENV_BRANCH__.__ENV_NAME__.srcclr.io',
        COLLECTOR_COOKIE_DOMAIN: '.srcclr.io',
        COLLECTOR_HOST: 'partlycloudy.ksp.ops2.srcclr.io',
        HELP_URL: 'https://help-__ENV_BRANCH__.__ENV_NAME__.srcclr.io',
        RECAPTCHA_KEY: '6LdhUhMUAAAAABhMNjEQopVH0P1zdFYb4HBvtiAI',
        REGISTRY_FRONTEND_DOMAIN: ['__ENV_NAME__.srcclr.io'],
        RELEASE_ID: "11509",
        SENTRY_URL: 'https://fbd12ad8ba4448c2bb7b4f54f0719144@sentry.__ENV_NAME__.srcclr.io/3',
        STRIPE_PUBLISHABLE_KEY: 'pk_test_z0Ei8WpOyg2kARAtCk3s3lfE',
        WWW_URL: 'https://www-__ENV_BRANCH__.__ENV_NAME__.srcclr.io',
        VC_WWW_URL: 'https://www.veracode.com',
        VC_INFO_URL: 'https://info.veracode.com',
        REGISTRY_URL: 'https://www-__ENV_BRANCH__.__ENV_NAME__.srcclr.io',
        VC_REGISTRY_URL: 'https://sca-__ENV_BRANCH__.__ENV_NAME__.veracode.io',
        SEPC_DOCKER_URL: 'docker.cd.srcclr.io/o2/sepc:latest',
        SEP_SERVER_IP: 'sep-tcp.__ENV_NAME__.srcclr.io',
        VERACODE_UIGATEWAY_HOST: 'https://ui-agora-__ENV_BRANCH__.__ENV_NAME__.veracode.io',
        VERACODE_LOGIN_HOST: 'https://web-agora-__ENV_BRANCH__.__ENV_NAME__.veracode.io/login',
        VERACODE_SCA_PLATFORM_UI_JS_FILE: 'https://sca-__ENV_BRANCH__.__ENV_NAME__.veracode.io/sca-platform-ui/runtime-es-bundle/bundle.umd.js',
        VERACODE_SCA_PLATFORM_UI_CSS_FILE: 'https://sca-__ENV_BRANCH__.__ENV_NAME__.veracode.io/sca-platform-ui/runtime-es-bundle/bundle.css',
        CSV_REQUEST_RATE: 1,
        CSV_REQUEST_PAGE_SIZE: 1000,
        VC_FEATURES: [{scanDate: true}, {policyRisk: true}, {latestScan: true}, {libraryCatalog: false}, {scaPlatformUI: true}]
      }
      window.SCA_PLATFORM_ENV = {
        AGENT_WORKSPACE_LINK_BASE_URL: 'https://sca-__ENV_BRANCH__.__ENV_NAME__.veracode.io',
        PLATFORM_BACKEND_URL: 'https://ui-agora-__ENV_BRANCH__.__ENV_NAME__.veracode.io/srcclr',
        START_A_SCAN: 'https://sca-__ENV_BRANCH__.__ENV_NAME__.veracode.io/start-scan',
        UPLOAD_APPLICATION_LINK_BASE_URL: 'https://analysiscenter-__ENV_BRANCH__.__ENV_NAME__.veracode.io/auth/index.jsp#',
        VERACODE_HOME: 'https://analysiscenter-__ENV_BRANCH__.__ENV_NAME__.veracode.io/auth/index.jsp#HomePage',
        VULN_DB_URL: 'https://sca-__ENV_BRANCH__.__ENV_NAME__.veracode.io/vulnerability-database/search'
      }
  - name: CSP_REPORT_URI
    value: https://sentry.__ENV_NAME__.srcclr.io/api/19/csp-report/?sentry_key=69d7963d74f14ea6b4134ee02016b4d9
  - name: CSP_REPORT_ONLY_URI
    value: https://sentry.__ENV_NAME__.srcclr.io/api/20/csp-report/?sentry_key=8ca43def466246008dc34ef25972dba5
resources:
  limits:
    memory: 512M
  requests:
    memory: 512M

